# LangChain 教学演示项目

这个项目包含6个独立的LangChain演示文件，每个都展示了LangChain框架的不同核心功能。所有演示都使用本地Ollama服务和llama3.2模型。

## 📋 项目结构

```
langchain_demo1/
├── requirements.txt          # 项目依赖
├── README.md                # 项目说明
├── demo1_basic_llm.py       # 演示1: 模型输入输出
├── demo2_knowledge_base.py  # 演示2: 基础知识库
├── demo3_chain_calls.py     # 演示3: 链式调用
├── demo4_memory.py          # 演示4: 内存记忆
├── demo5_simple_agent.py    # 演示5: 简单智能体
└── demo6_callback_logging.py # 演示6: 回调函数记录审计日志
```

## 🚀 快速开始

### 1. 环境准备

#### 安装Ollama
```bash
# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# 下载并安装 https://ollama.ai/download
```

#### 启动Ollama服务
```bash
ollama serve
```

#### 下载llama3.2模型
```bash
ollama pull llama3.2
```

### 2. 安装Python依赖

```bash
# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 3. 运行演示

每个演示文件都可以独立运行：

```bash
# 演示1: 基础模型输入输出
python demo1_basic_llm.py

# 演示2: 基础知识库
python demo2_knowledge_base.py

# 演示3: 链式调用
python demo3_chain_calls.py

# 演示4: 内存记忆
python demo4_memory.py

# 演示5: 简单智能体
python demo5_simple_agent.py

# 演示6: 回调函数记录审计日志
python demo6_callback_logging.py
```

## 📚 演示内容详解

### Demo 1: 基础模型输入输出 (`demo1_basic_llm.py`)

**学习目标**: 了解LangChain的基本使用方法

**功能演示**:
- 简单对话交互
- 系统提示的使用
- 批量处理多个问题

**核心概念**:
- `ChatOllama` 模型初始化
- `HumanMessage` 和 `SystemMessage`
- 模型的 `invoke()` 和 `batch()` 方法

### Demo 2: 基础知识库 (`demo2_knowledge_base.py`)

**学习目标**: 掌握向量数据库和RAG（检索增强生成）

**功能演示**:
- 创建向量数据库
- 文档相似性搜索
- RAG查询实现
- 动态添加文档

**核心概念**:
- `OllamaEmbeddings` 嵌入模型
- `Chroma` 向量数据库
- `Document` 文档对象
- RAG链的构建

### Demo 3: 链式调用 (`demo3_chain_calls.py`)

**学习目标**: 理解LangChain的链式编程模式

**功能演示**:
- 简单链式调用
- 顺序链处理
- 条件链路由
- 并行链执行

**核心概念**:
- `ChatPromptTemplate` 提示模板
- `RunnablePassthrough` 数据传递
- `RunnableLambda` 自定义函数
- 链的组合和嵌套

### Demo 4: 内存记忆 (`demo4_memory.py`)

**学习目标**: 实现对话上下文的持久化

**功能演示**:
- 简单对话历史
- 缓冲区内存管理
- 对话摘要内存
- 自定义内存实现

**核心概念**:
- `ConversationBufferMemory` 缓冲内存
- `ConversationSummaryMemory` 摘要内存
- `MessagesPlaceholder` 消息占位符
- 内存的保存和加载

### Demo 5: 简单智能体 (`demo5_simple_agent.py`)

**学习目标**: 构建具有工具使用能力的智能体

**功能演示**:
- 工具定义和注册
- 智能体推理和工具调用
- 多步骤任务处理
- 工具执行结果整合

**核心概念**:
- 工具函数的定义
- 智能体的推理过程
- 工具调用的解析
- 复杂任务的分解

### Demo 6: 回调函数记录审计日志 (`demo6_callback_logging.py`)

**学习目标**: 监控和记录LLM应用的运行状态

**功能演示**:
- 审计日志记录
- 性能监控
- 错误追踪
- 自定义回调处理

**核心概念**:
- `BaseCallbackHandler` 回调基类
- 生命周期事件处理
- 日志记录和格式化
- 性能指标收集

## 🎯 教学建议

### 学习顺序
1. **Demo 1** - 建立基础概念
2. **Demo 3** - 理解链式编程
3. **Demo 2** - 掌握知识库技术
4. **Demo 4** - 学习内存管理
5. **Demo 5** - 构建智能体
6. **Demo 6** - 监控和调试

### 实践练习
每个演示后可以尝试：
1. 修改提示模板
2. 调整模型参数
3. 添加新的功能
4. 组合不同的演示

### 常见问题

**Q: Ollama连接失败怎么办？**
A: 确保Ollama服务正在运行 (`ollama serve`) 并且端口11434可访问

**Q: 模型响应很慢？**
A: 这是正常的，本地模型推理需要时间，可以降低temperature参数

**Q: 内存不足？**
A: llama3.2模型需要较多内存，可以尝试使用更小的模型如llama3.2:1b

**Q: 依赖安装失败？**
A: 建议使用虚拟环境，并确保Python版本>=3.8

## 🔧 自定义配置

### 更换模型
在每个演示文件中修改模型配置：
```python
self.llm = ChatOllama(
    model="llama3.2:1b",  # 使用更小的模型
    base_url="http://localhost:11434",
    temperature=0.1  # 调整创造性
)
```

### 修改服务地址
如果Ollama运行在其他地址：
```python
base_url="http://your-server:11434"
```

## 📖 扩展学习

- [LangChain官方文档](https://python.langchain.com/)
- [Ollama官方网站](https://ollama.ai/)
- [向量数据库概念](https://www.pinecone.io/learn/vector-database/)
- [RAG技术详解](https://python.langchain.com/docs/use_cases/question_answering/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这些演示！

## 📄 许可证

MIT License
