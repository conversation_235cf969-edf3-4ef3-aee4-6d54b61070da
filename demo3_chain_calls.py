#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo 3: LangChain 链式调用演示
演示如何创建和使用不同类型的链式调用
"""

from langchain_ollama import ChatOllama
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from operator import itemgetter


class ChainCallsDemo:
    """链式调用演示类"""
    
    def __init__(self):
        """初始化模型"""
        print("🚀 初始化链式调用系统...")
        self.llm = ChatOllama(
            model="llama3.2",
            base_url="http://localhost:11434",
            temperature=0.3
        )
        print("✅ 链式调用系统初始化完成！")
    
    def simple_chain_demo(self):
        """简单链式调用演示"""
        print("\n" + "=" * 40)
        print("📝 演示1: 简单链式调用")
        print("=" * 40)
        
        # 创建提示模板
        prompt = ChatPromptTemplate.from_template(
            "请用一句话总结以下内容：{text}"
        )
        
        # 创建输出解析器
        output_parser = StrOutputParser()
        
        # 创建链
        chain = prompt | self.llm | output_parser
        
        # 测试数据
        test_text = """
        人工智能（AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。
        这包括学习、推理、问题解决、感知和语言理解。AI技术已经在许多领域得到应用，
        包括医疗保健、金融、交通和娱乐。
        """
        
        print(f"📄 原文: {test_text.strip()}")
        
        # 执行链
        result = chain.invoke({"text": test_text})
        print(f"📝 总结: {result}")
        
        return result
    
    def sequential_chain_demo(self):
        """顺序链演示"""
        print("\n" + "=" * 40)
        print("📝 演示2: 顺序链调用")
        print("=" * 40)
        
        # 第一个链：生成故事大纲
        outline_prompt = ChatPromptTemplate.from_template(
            "为主题'{topic}'创建一个简短的故事大纲，包含开头、发展和结尾。"
        )
        outline_chain = outline_prompt | self.llm | StrOutputParser()
        
        # 第二个链：扩展故事
        story_prompt = ChatPromptTemplate.from_template(
            "基于以下大纲，写一个200字左右的完整故事：\n{outline}"
        )
        story_chain = story_prompt | self.llm | StrOutputParser()
        
        # 第三个链：提取关键词
        keywords_prompt = ChatPromptTemplate.from_template(
            "从以下故事中提取5个关键词：\n{story}"
        )
        keywords_chain = keywords_prompt | self.llm | StrOutputParser()
        
        # 组合成顺序链
        full_chain = (
            {"topic": RunnablePassthrough()}
            | RunnablePassthrough.assign(outline=outline_chain)
            | RunnablePassthrough.assign(story=story_chain)
            | RunnablePassthrough.assign(keywords=keywords_chain)
        )
        
        # 执行链
        topic = "太空探险"
        print(f"🚀 主题: {topic}")
        
        result = full_chain.invoke(topic)
        
        print(f"\n📋 故事大纲:\n{result['outline']}")
        print(f"\n📖 完整故事:\n{result['story']}")
        print(f"\n🔑 关键词:\n{result['keywords']}")
        
        return result
    
    def conditional_chain_demo(self):
        """条件链演示"""
        print("\n" + "=" * 40)
        print("📝 演示3: 条件链调用")
        print("=" * 40)
        
        # 分类提示
        classification_prompt = ChatPromptTemplate.from_template(
            """请判断以下问题的类型，只回答以下选项之一：
            - "技术问题"
            - "生活问题"
            - "学习问题"
            
            问题：{question}
            类型："""
        )
        
        # 不同类型的回答提示
        tech_prompt = ChatPromptTemplate.from_template(
            "作为技术专家，请详细回答这个技术问题：{question}"
        )
        
        life_prompt = ChatPromptTemplate.from_template(
            "作为生活顾问，请给出实用的生活建议：{question}"
        )
        
        study_prompt = ChatPromptTemplate.from_template(
            "作为学习导师，请提供学习方法和建议：{question}"
        )
        
        # 创建分类链
        classification_chain = classification_prompt | self.llm | StrOutputParser()
        
        # 条件路由函数
        def route_question(inputs):
            question = inputs["question"]
            category = inputs["category"].strip()
            
            print(f"🏷️  问题分类: {category}")
            
            if "技术" in category:
                chain = tech_prompt | self.llm | StrOutputParser()
            elif "生活" in category:
                chain = life_prompt | self.llm | StrOutputParser()
            elif "学习" in category:
                chain = study_prompt | self.llm | StrOutputParser()
            else:
                # 默认处理
                default_prompt = ChatPromptTemplate.from_template("请回答这个问题：{question}")
                chain = default_prompt | self.llm | StrOutputParser()
            
            return chain.invoke({"question": question})
        
        # 创建完整的条件链
        conditional_chain = (
            {"question": RunnablePassthrough()}
            | RunnablePassthrough.assign(category=classification_chain)
            | RunnableLambda(route_question)
        )
        
        # 测试不同类型的问题
        questions = [
            "如何优化Python代码的性能？",
            "如何保持健康的作息时间？",
            "如何提高英语口语水平？"
        ]
        
        for question in questions:
            print(f"\n❓ 问题: {question}")
            answer = conditional_chain.invoke(question)
            print(f"💡 回答: {answer}")
    
    def parallel_chain_demo(self):
        """并行链演示"""
        print("\n" + "=" * 40)
        print("📝 演示4: 并行链调用")
        print("=" * 40)
        
        # 创建多个并行的分析链
        sentiment_prompt = ChatPromptTemplate.from_template(
            "分析以下文本的情感倾向（积极/消极/中性）：{text}"
        )
        
        summary_prompt = ChatPromptTemplate.from_template(
            "用一句话总结以下文本：{text}"
        )
        
        keywords_prompt = ChatPromptTemplate.from_template(
            "提取以下文本的3个关键词：{text}"
        )
        
        # 创建并行链
        parallel_chain = {
            "sentiment": sentiment_prompt | self.llm | StrOutputParser(),
            "summary": summary_prompt | self.llm | StrOutputParser(),
            "keywords": keywords_prompt | self.llm | StrOutputParser(),
            "original": itemgetter("text")  # 保留原文
        }
        
        # 测试文本
        test_text = """
        今天是一个美好的日子！阳光明媚，我和朋友们一起去公园野餐。
        我们准备了很多美味的食物，玩了各种游戏，度过了愉快的时光。
        这样的日子让我感到非常幸福和满足。
        """
        
        print(f"📄 原文: {test_text.strip()}")
        
        # 执行并行链
        result = parallel_chain.invoke({"text": test_text})
        
        print(f"\n😊 情感分析: {result['sentiment']}")
        print(f"📝 文本总结: {result['summary']}")
        print(f"🔑 关键词: {result['keywords']}")
        
        return result


def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("🎯 LangChain Demo 3: 链式调用 - 交互式菜单")
    print("=" * 60)
    print("请选择要运行的演示:")
    print("1. 简单链式调用")
    print("2. 顺序链调用")
    print("3. 条件链调用")
    print("4. 并行链调用")
    print("5. 运行所有演示")
    print("0. 退出")
    print("=" * 60)


def main():
    """主函数 - 演示链式调用功能"""
    print("=" * 60)
    print("🎯 LangChain Demo 3: 链式调用")
    print("=" * 60)

    try:
        # 初始化演示类
        demo = ChainCallsDemo()

        while True:
            show_menu()
            choice = input("请输入选择 (0-5): ").strip()
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                demo.simple_chain_demo()
            elif choice == "2":
                demo.sequential_chain_demo()
            elif choice == "3":
                demo.conditional_chain_demo()
            elif choice == "4":
                demo.parallel_chain_demo()
            elif choice == "5":
                print("\n🚀 运行所有演示...")
                demo.simple_chain_demo()
                demo.sequential_chain_demo()
                demo.conditional_chain_demo()
                demo.parallel_chain_demo()
                print("\n🎉 所有演示完成！")
            else:
                print("❌ 无效选择，请输入 0-5")

            if choice != "0":
                input("\n按回车键继续...")

    except KeyboardInterrupt:
        print("\n👋 用户中断，再见！")
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保 Ollama 服务正在运行，并且已安装 llama3.2 模型")


if __name__ == "__main__":
    main()
