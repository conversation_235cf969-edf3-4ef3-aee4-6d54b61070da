2025-07-04 19:46:12,243 - INFO - {"session_id": "20250704_194612", "event_type": "SESSION_START", "timestamp": "2025-07-04T19:46:12.243128", "data": {"session_id": "20250704_194612", "timestamp": "2025-07-04T19:46:12.243126"}}
2025-07-04 19:46:12,253 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:12,436 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
2025-07-04 19:46:13,075 - INFO - {"session_id": "20250704_194612", "event_type": "LLM_END", "timestamp": "2025-07-04T19:46:13.075697", "data": {"call_id": "call_1", "duration_seconds": 0.822, "response_length": 1020, "token_usage": null, "response_preview": "generations=[[ChatGeneration(generation_info={'model': 'llama3.2', 'created_at': '2025-07-04T11:46:13.072017Z', 'done': True, 'done_reason': 'stop', 'total_duration': 816597250, 'load_duration': 40713..."}}
2025-07-04 19:46:13,076 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:13,143 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
2025-07-04 19:46:14,244 - INFO - {"session_id": "20250704_194612", "event_type": "LLM_END", "timestamp": "2025-07-04T19:46:14.244166", "data": {"call_id": "call_2", "duration_seconds": 1.168, "response_length": 1159, "token_usage": null, "response_preview": "generations=[[ChatGeneration(generation_info={'model': 'llama3.2', 'created_at': '2025-07-04T11:46:14.242865Z', 'done': True, 'done_reason': 'stop', 'total_duration': 1166205917, 'load_duration': 1869..."}}
2025-07-04 19:46:14,245 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:14,313 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
2025-07-04 19:46:15,195 - INFO - {"session_id": "20250704_194612", "event_type": "LLM_END", "timestamp": "2025-07-04T19:46:15.195537", "data": {"call_id": "call_3", "duration_seconds": 0.95, "response_length": 1163, "token_usage": null, "response_preview": "generations=[[ChatGeneration(generation_info={'model': 'llama3.2', 'created_at': '2025-07-04T11:46:15.193952Z', 'done': True, 'done_reason': 'stop', 'total_duration': 947727958, 'load_duration': 17675..."}}
2025-07-04 19:46:15,210 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:15,213 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
2025-07-04 19:46:15,213 - WARNING - Error in AuditCallbackHandler.on_llm_error callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:15,214 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:15,282 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
2025-07-04 19:46:15,620 - INFO - {"session_id": "20250704_194612", "event_type": "LLM_END", "timestamp": "2025-07-04T19:46:15.620113", "data": {"call_id": "call_5", "duration_seconds": 0.405, "response_length": 956, "token_usage": null, "response_preview": "generations=[[ChatGeneration(generation_info={'model': 'llama3.2', 'created_at': '2025-07-04T11:46:15.618359Z', 'done': True, 'done_reason': 'stop', 'total_duration': 403586708, 'load_duration': 18183..."}}
2025-07-04 19:46:15,622 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:15,690 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
2025-07-04 19:46:16,088 - INFO - {"session_id": "20250704_194612", "event_type": "LLM_END", "timestamp": "2025-07-04T19:46:16.088623", "data": {"call_id": "call_6", "duration_seconds": 0.466, "response_length": 968, "token_usage": null, "response_preview": "generations=[[ChatGeneration(generation_info={'model': 'llama3.2', 'created_at': '2025-07-04T11:46:16.086989Z', 'done': True, 'done_reason': 'stop', 'total_duration': 463606209, 'load_duration': 17364..."}}
2025-07-04 19:46:16,090 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:16,158 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
2025-07-04 19:46:16,484 - INFO - {"session_id": "20250704_194612", "event_type": "LLM_END", "timestamp": "2025-07-04T19:46:16.484477", "data": {"call_id": "call_7", "duration_seconds": 0.394, "response_length": 952, "token_usage": null, "response_preview": "generations=[[ChatGeneration(generation_info={'model': 'llama3.2', 'created_at': '2025-07-04T11:46:16.48289Z', 'done': True, 'done_reason': 'stop', 'total_duration': 390866834, 'load_duration': 177370..."}}
2025-07-04 19:46:16,486 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:16,554 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
2025-07-04 19:46:16,989 - INFO - {"session_id": "20250704_194612", "event_type": "LLM_END", "timestamp": "2025-07-04T19:46:16.989909", "data": {"call_id": "call_8", "duration_seconds": 0.503, "response_length": 982, "token_usage": null, "response_preview": "generations=[[ChatGeneration(generation_info={'model': 'llama3.2', 'created_at': '2025-07-04T11:46:16.988279Z', 'done': True, 'done_reason': 'stop', 'total_duration': 500334708, 'load_duration': 17479..."}}
2025-07-04 19:46:16,991 - WARNING - Error in AuditCallbackHandler.on_llm_start callback: TypeError('Object of type UUID is not JSON serializable')
2025-07-04 19:46:17,060 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
2025-07-04 19:46:17,531 - INFO - {"session_id": "20250704_194612", "event_type": "LLM_END", "timestamp": "2025-07-04T19:46:17.531681", "data": {"call_id": "call_9", "duration_seconds": 0.54, "response_length": 1004, "token_usage": null, "response_preview": "generations=[[ChatGeneration(generation_info={'model': 'llama3.2', 'created_at': '2025-07-04T11:46:17.530493Z', 'done': True, 'done_reason': 'stop', 'total_duration': 536974500, 'load_duration': 17911..."}}
2025-07-04 19:46:17,615 - INFO - HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
