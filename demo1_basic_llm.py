#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo 1: Lang<PERSON>hain 基础模型输入输出演示
演示如何使用 ChatOllama 进行基本的对话交互
"""

from langchain_ollama import ChatOllama
from langchain_core.messages import HumanMessage, SystemMessage


class BasicLLMDemo:
    """基础大语言模型演示类"""
    
    def __init__(self):
        """初始化模型"""
        print("🚀 初始化 ChatOllama 模型...")
        self.llm = ChatOllama(
            model="llama3.2",
            base_url="http://localhost:11434",
            temperature=0.3  # 降低温度，提高分析准确性
        )
        print("✅ 模型初始化完成！")
    
    def simple_chat(self, user_input: str) -> str:
        """简单对话功能"""
        print(f"\n👤 用户输入: {user_input}")
        
        # 创建消息
        messages = [HumanMessage(content=user_input)]
        
        # 调用模型
        response = self.llm.invoke(messages)
        
        print(f"🤖 AI回复: {response.content}")
        return response.content
    
    def system_prompt_chat(self, system_prompt: str, user_input: str) -> str:
        """带系统提示的对话"""
        print(f"\n🔧 系统提示: {system_prompt}")
        print(f"👤 用户输入: {user_input}")
        
        # 创建带系统提示的消息
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_input)
        ]
        
        # 调用模型
        response = self.llm.invoke(messages)
        
        print(f"🤖 AI回复: {response.content}")
        return response.content
    
    def batch_chat(self, questions: list) -> list:
        """批量处理多个问题"""
        print(f"\n📦 批量处理 {len(questions)} 个问题...")
        
        # 准备批量消息
        message_batches = [[HumanMessage(content=q)] for q in questions]
        
        # 批量调用
        responses = self.llm.batch(message_batches)
        
        results = []
        for i, (question, response) in enumerate(zip(questions, responses)):
            print(f"\n问题 {i+1}: {question}")
            print(f"回答 {i+1}: {response.content}")
            results.append(response.content)
        
        return results


def main():
    """主函数 - 演示各种功能"""
    print("=" * 60)
    print("🎯 LangChain Demo 1: 基础模型输入输出")
    print("=" * 60)
    
    try:
        # 初始化演示类
        demo = BasicLLMDemo()
        
        # 1. 简单对话演示
        print("\n" + "=" * 40)
        print("📝 演示1: 简单对话")
        print("=" * 40)
        demo.simple_chat("你好，请介绍一下自己")
        demo.simple_chat("什么是人工智能？")

        # 2. 系统提示演示
        print("\n" + "=" * 40)
        print("📝 演示2: 系统提示对话")
        print("=" * 40)
        system_prompt = "你是一个专业的Python编程助手，请用简洁明了的方式回答编程问题。"
        demo.system_prompt_chat(system_prompt, "如何在Python中创建一个列表？")
        
        # 3. 批量处理演示
        print("\n" + "=" * 40)
        print("📝 演示3: 批量处理")
        print("=" * 40)
        questions = [
            "什么是机器学习？",
            "Python有哪些优势？",
            "如何学习编程？"
        ]
        demo.batch_chat(questions)
        
        print("\n" + "=" * 60)
        print("🎉 Demo 1 演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保 Ollama 服务正在运行，并且已安装 llama3.2 模型")
        print("启动命令: ollama serve")
        print("安装模型: ollama pull llama3.2")


if __name__ == "__main__":
    main()
