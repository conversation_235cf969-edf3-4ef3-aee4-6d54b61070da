#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo 5: LangChain 简单智能体演示
演示如何创建具有工具使用能力的智能体
"""

from langchain_ollama import ChatOllama
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
import json
import re
import math
import datetime


class SimpleAgentDemo:
    """简单智能体演示类"""
    
    def __init__(self):
        """初始化智能体"""
        print("🚀 初始化智能体系统...")
        self.llm = ChatOllama(
            model="llama3.2",
            base_url="http://localhost:11434",
            temperature=0.3
        )
        
        # 定义可用工具
        self.tools = {
            "calculator": self.calculator_tool,
            "weather": self.weather_tool,
            "time": self.time_tool,
            "search": self.search_tool
        }
        
        print("✅ 智能体系统初始化完成！")
        print(f"📦 可用工具: {list(self.tools.keys())}")
    
    def calculator_tool(self, expression: str) -> str:
        """计算器工具"""
        try:
            # 安全的数学表达式计算
            allowed_chars = set('0123456789+-*/()., ')
            if not all(c in allowed_chars for c in expression):
                return "错误：表达式包含不允许的字符"
            
            # 替换一些常见的数学函数
            expression = expression.replace('sqrt', 'math.sqrt')
            expression = expression.replace('sin', 'math.sin')
            expression = expression.replace('cos', 'math.cos')
            expression = expression.replace('tan', 'math.tan')
            expression = expression.replace('log', 'math.log')
            
            result = eval(expression, {"__builtins__": {}, "math": math})
            return f"计算结果: {result}"
        except Exception as e:
            return f"计算错误: {str(e)}"
    
    def weather_tool(self, city: str) -> str:
        """天气查询工具（模拟）"""
        # 模拟天气数据
        weather_data = {
            "北京": "晴天，温度 15°C，湿度 45%",
            "上海": "多云，温度 18°C，湿度 60%",
            "广州": "小雨，温度 22°C，湿度 80%",
            "深圳": "晴天，温度 25°C，湿度 55%",
            "杭州": "阴天，温度 16°C，湿度 70%"
        }
        
        return weather_data.get(city, f"抱歉，暂时无法获取{city}的天气信息")
    
    def time_tool(self, format_type: str = "datetime") -> str:
        """时间查询工具"""
        now = datetime.datetime.now()
        
        if format_type == "date":
            return f"今天是 {now.strftime('%Y年%m月%d日')}"
        elif format_type == "time":
            return f"现在时间是 {now.strftime('%H:%M:%S')}"
        else:
            return f"现在是 {now.strftime('%Y年%m月%d日 %H:%M:%S')}"
    
    def search_tool(self, query: str) -> str:
        """搜索工具（模拟）"""
        # 模拟搜索结果
        search_results = {
            "python": "Python是一种高级编程语言，广泛用于Web开发、数据科学、人工智能等领域。",
            "机器学习": "机器学习是人工智能的一个分支，让计算机能够从数据中学习而无需明确编程。",
            "langchain": "LangChain是一个用于开发由大语言模型驱动的应用程序的框架。",
            "人工智能": "人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。"
        }
        
        for key, value in search_results.items():
            if key.lower() in query.lower():
                return f"搜索结果: {value}"
        
        return f"抱歉，没有找到关于'{query}'的相关信息"
    
    def parse_tool_call(self, text: str):
        """解析工具调用"""
        # 查找工具调用模式
        tool_pattern = r'使用工具\s*(\w+)\s*:\s*(.+?)(?=\n|$)'
        matches = re.findall(tool_pattern, text, re.IGNORECASE)
        
        if not matches:
            # 尝试其他模式
            tool_pattern = r'调用\s*(\w+)\s*工具\s*:\s*(.+?)(?=\n|$)'
            matches = re.findall(tool_pattern, text, re.IGNORECASE)
        
        return matches
    
    def execute_tool(self, tool_name: str, tool_input: str) -> str:
        """执行工具"""
        if tool_name.lower() in self.tools:
            tool_func = self.tools[tool_name.lower()]
            return tool_func(tool_input.strip())
        else:
            return f"错误：未知工具 '{tool_name}'"
    
    def agent_chat(self, user_input: str) -> str:
        """智能体对话"""
        # 创建智能体提示
        agent_prompt = ChatPromptTemplate.from_template("""
你是一个智能助手，可以使用以下工具来帮助用户：

可用工具：
1. calculator - 计算数学表达式，使用格式：使用工具 calculator: 表达式
2. weather - 查询城市天气，使用格式：使用工具 weather: 城市名
3. time - 查询当前时间，使用格式：使用工具 time: datetime/date/time
4. search - 搜索信息，使用格式：使用工具 search: 搜索内容

用户问题: {question}

请分析用户问题，如果需要使用工具，请按照指定格式调用工具。如果不需要工具，直接回答问题。

回答:""")
        
        # 第一步：分析是否需要使用工具
        chain = agent_prompt | self.llm | StrOutputParser()
        response = chain.invoke({"question": user_input})
        
        print(f"🤖 智能体分析: {response}")
        
        # 第二步：解析并执行工具调用
        tool_calls = self.parse_tool_call(response)
        
        if tool_calls:
            tool_results = []
            for tool_name, tool_input in tool_calls:
                print(f"🔧 执行工具: {tool_name}({tool_input})")
                result = self.execute_tool(tool_name, tool_input)
                tool_results.append(f"{tool_name}: {result}")
                print(f"📊 工具结果: {result}")
            
            # 第三步：基于工具结果生成最终回答
            final_prompt = ChatPromptTemplate.from_template("""
基于以下工具执行结果，为用户提供完整的回答：

用户问题: {question}
工具结果: {tool_results}

请整合工具结果，给出清晰、有用的回答：""")
            
            final_chain = final_prompt | self.llm | StrOutputParser()
            final_response = final_chain.invoke({
                "question": user_input,
                "tool_results": "\n".join(tool_results)
            })
            
            return final_response
        else:
            return response
    
    def multi_step_agent_demo(self):
        """多步骤智能体演示"""
        print("\n" + "=" * 40)
        print("📝 演示: 多步骤任务处理")
        print("=" * 40)
        
        # 复杂任务：计划一天的行程
        complex_task = """
        我明天要去北京出差，请帮我：
        1. 查询北京的天气情况
        2. 告诉我现在的时间
        3. 计算一下如果我早上8点出发，下午6点回来，总共在外面多少小时
        4. 搜索一下北京的相关信息
        """
        
        print(f"👤 用户: {complex_task}")
        
        # 分步处理任务
        steps = [
            "查询北京天气",
            "查询当前时间",
            "计算 18 - 8",
            "搜索北京信息"
        ]
        
        results = []
        for i, step in enumerate(steps, 1):
            print(f"\n🔄 步骤 {i}: {step}")
            result = self.agent_chat(step)
            results.append(f"步骤{i}: {result}")
            print(f"✅ 完成: {result}")
        
        # 整合所有结果
        final_prompt = ChatPromptTemplate.from_template("""
        基于以下各步骤的结果，为用户的出差计划提供一个完整的总结：

        原始任务: {task}
        
        执行结果:
        {results}
        
        请提供一个清晰、有条理的总结：""")
        
        final_chain = final_prompt | self.llm | StrOutputParser()
        summary = final_chain.invoke({
            "task": complex_task,
            "results": "\n".join(results)
        })
        
        print(f"\n📋 最终总结: {summary}")
        return summary


def main():
    """主函数 - 演示智能体功能"""
    print("=" * 60)
    print("🎯 LangChain Demo 5: 简单智能体")
    print("=" * 60)
    
    try:
        # 初始化演示类
        demo = SimpleAgentDemo()
        
        # 1. 基础工具使用演示
        print("\n" + "=" * 40)
        print("📝 演示1: 基础工具使用")
        print("=" * 40)
        
        test_questions = [
            "帮我计算 25 * 4 + 10",
            "查询一下上海的天气",
            "现在几点了？",
            "搜索一下Python的相关信息"
        ]
        
        for question in test_questions:
            print(f"\n👤 用户: {question}")
            response = demo.agent_chat(question)
            print(f"🤖 智能体: {response}")
        
        # 2. 多步骤任务演示
        demo.multi_step_agent_demo()
        
        # 3. 交互式对话演示
        print("\n" + "=" * 40)
        print("📝 演示3: 混合对话")
        print("=" * 40)
        
        mixed_questions = [
            "你好，我想了解一下今天的日期",
            "如果我有100元，买了价值35元的东西，还剩多少钱？",
            "广州今天天气怎么样？",
            "谢谢你的帮助！"
        ]
        
        for question in mixed_questions:
            print(f"\n👤 用户: {question}")
            response = demo.agent_chat(question)
            print(f"🤖 智能体: {response}")
        
        print("\n" + "=" * 60)
        print("🎉 Demo 5 演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保 Ollama 服务正在运行，并且已安装 llama3.2 模型")


if __name__ == "__main__":
    main()
