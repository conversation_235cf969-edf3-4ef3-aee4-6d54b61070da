#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo 4: Lang<PERSON>hain 内存记忆演示
演示如何使用不同类型的内存来维持对话上下文
"""

from langchain_ollama import ChatOllama
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.messages import HumanMessage, AIMessage
import json


class MemoryDemo:
    """内存记忆演示类"""
    
    def __init__(self):
        """初始化模型和内存"""
        print("🚀 初始化内存系统...")
        self.llm = ChatOllama(
            model="llama3.2",
            base_url="http://localhost:11434",
            temperature=0.3
        )
        print("✅ 内存系统初始化完成！")
    
    def simple_memory_demo(self):
        """简单内存演示"""
        print("\n" + "=" * 40)
        print("📝 演示1: 简单对话内存")
        print("=" * 40)
        
        # 创建简单的对话历史存储
        conversation_history = []
        
        def chat_with_memory(user_input: str):
            # 添加用户消息到历史
            conversation_history.append(HumanMessage(content=user_input))
            
            # 创建包含历史的提示
            messages = conversation_history.copy()
            
            # 调用模型
            response = self.llm.invoke(messages)
            
            # 添加AI回复到历史
            conversation_history.append(AIMessage(content=response.content))
            
            return response.content
        
        # 模拟多轮对话
        conversations = [
            "你好，我叫张三",
            "我今年25岁",
            "我喜欢编程",
            "你还记得我的名字吗？",
            "我的年龄是多少？"
        ]
        
        for user_msg in conversations:
            print(f"\n👤 用户: {user_msg}")
            ai_response = chat_with_memory(user_msg)
            print(f"🤖 AI: {ai_response}")
    
    def buffer_memory_demo(self):
        """缓冲区内存演示"""
        print("\n" + "=" * 40)
        print("📝 演示2: 缓冲区内存")
        print("=" * 40)
        
        # 创建缓冲区内存
        memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        # 创建带内存的提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个友好的助手，能够记住之前的对话内容。"),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}")
        ])
        
        # 创建链
        chain = prompt | self.llm | StrOutputParser()
        
        def chat_with_buffer_memory(user_input: str):
            # 获取内存中的对话历史
            memory_variables = memory.load_memory_variables({})
            
            # 调用链
            response = chain.invoke({
                "input": user_input,
                "chat_history": memory_variables["chat_history"]
            })
            
            # 保存对话到内存
            memory.save_context(
                {"input": user_input},
                {"output": response}
            )
            
            return response
        
        # 测试对话
        conversations = [
            "我正在学习Python编程",
            "我遇到了一个关于列表的问题",
            "如何向列表中添加元素？",
            "刚才我说我在学习什么？"
        ]
        
        for user_msg in conversations:
            print(f"\n👤 用户: {user_msg}")
            ai_response = chat_with_buffer_memory(user_msg)
            print(f"🤖 AI: {ai_response}")
        
        # 显示内存内容
        print(f"\n💾 内存内容:")
        memory_vars = memory.load_memory_variables({})
        for i, msg in enumerate(memory_vars["chat_history"]):
            role = "用户" if isinstance(msg, HumanMessage) else "AI"
            print(f"  {i+1}. {role}: {msg.content[:50]}...")
    
    def summary_memory_demo(self):
        """摘要内存演示"""
        print("\n" + "=" * 40)
        print("📝 演示3: 摘要内存")
        print("=" * 40)
        
        # 创建摘要内存
        memory = ConversationSummaryMemory(
            llm=self.llm,
            memory_key="chat_history",
            return_messages=True
        )
        
        # 创建带内存的提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个助手。以下是之前对话的摘要：{chat_history}"),
            ("human", "{input}")
        ])
        
        # 创建链
        chain = prompt | self.llm | StrOutputParser()
        
        def chat_with_summary_memory(user_input: str):
            # 获取内存摘要
            memory_variables = memory.load_memory_variables({})
            
            # 调用链
            response = chain.invoke({
                "input": user_input,
                "chat_history": memory_variables.get("chat_history", "")
            })
            
            # 保存对话到内存
            memory.save_context(
                {"input": user_input},
                {"output": response}
            )
            
            return response
        
        # 模拟长对话
        long_conversations = [
            "我是一名软件工程师，在一家科技公司工作",
            "我们公司主要开发移动应用程序",
            "我负责后端API的开发，主要使用Python和Django",
            "最近我们在开发一个电商平台",
            "这个平台需要处理大量的用户数据和交易信息",
            "我们使用了微服务架构来提高系统的可扩展性",
            "你能总结一下我刚才说的关于我工作的信息吗？"
        ]
        
        for user_msg in long_conversations:
            print(f"\n👤 用户: {user_msg}")
            ai_response = chat_with_summary_memory(user_msg)
            print(f"🤖 AI: {ai_response}")
        
        # 显示摘要
        print(f"\n📋 对话摘要:")
        summary = memory.load_memory_variables({})
        print(f"  {summary.get('chat_history', '暂无摘要')}")
    
    def custom_memory_demo(self):
        """自定义内存演示"""
        print("\n" + "=" * 40)
        print("📝 演示4: 自定义内存管理")
        print("=" * 40)
        
        class CustomMemory:
            """自定义内存类"""
            
            def __init__(self, max_messages=6):
                self.messages = []
                self.max_messages = max_messages
                self.user_info = {}  # 存储用户信息
            
            def add_message(self, role: str, content: str):
                """添加消息"""
                self.messages.append({"role": role, "content": content})
                
                # 保持消息数量在限制内
                if len(self.messages) > self.max_messages:
                    self.messages = self.messages[-self.max_messages:]
                
                # 提取用户信息
                if role == "user":
                    self._extract_user_info(content)
            
            def _extract_user_info(self, content: str):
                """提取用户信息"""
                content_lower = content.lower()
                if "我叫" in content_lower or "我的名字" in content_lower:
                    # 简单的名字提取
                    words = content.split()
                    for i, word in enumerate(words):
                        if word in ["我叫", "名字是", "叫"]:
                            if i + 1 < len(words):
                                self.user_info["name"] = words[i + 1]
                
                if "岁" in content_lower:
                    # 简单的年龄提取
                    import re
                    age_match = re.search(r'(\d+)岁', content)
                    if age_match:
                        self.user_info["age"] = age_match.group(1)
            
            def get_context(self):
                """获取上下文"""
                context = ""
                if self.user_info:
                    context += f"用户信息: {json.dumps(self.user_info, ensure_ascii=False)}\n"
                
                context += "最近的对话:\n"
                for msg in self.messages[-4:]:  # 只显示最近4条消息
                    role = "用户" if msg["role"] == "user" else "助手"
                    context += f"{role}: {msg['content']}\n"
                
                return context
        
        # 创建自定义内存实例
        custom_memory = CustomMemory()
        
        # 创建提示模板
        prompt = ChatPromptTemplate.from_template(
            """基于以下上下文信息回答用户问题：

{context}

当前问题: {question}

回答:"""
        )
        
        # 创建链
        chain = prompt | self.llm | StrOutputParser()
        
        def chat_with_custom_memory(user_input: str):
            # 添加用户消息到内存
            custom_memory.add_message("user", user_input)
            
            # 获取上下文
            context = custom_memory.get_context()
            
            # 调用链
            response = chain.invoke({
                "context": context,
                "question": user_input
            })
            
            # 添加AI回复到内存
            custom_memory.add_message("assistant", response)
            
            return response
        
        # 测试自定义内存
        conversations = [
            "你好，我叫李明",
            "我今年30岁",
            "我是一名教师",
            "我教数学",
            "你还记得我的职业吗？",
            "我的年龄是多少？",
            "我们聊了很多话题，你能总结一下吗？"
        ]
        
        for user_msg in conversations:
            print(f"\n👤 用户: {user_msg}")
            ai_response = chat_with_custom_memory(user_msg)
            print(f"🤖 AI: {ai_response}")
        
        # 显示内存状态
        print(f"\n💾 自定义内存状态:")
        print(f"  用户信息: {custom_memory.user_info}")
        print(f"  消息数量: {len(custom_memory.messages)}")


def main():
    """主函数 - 演示内存功能"""
    print("=" * 60)
    print("🎯 LangChain Demo 4: 内存记忆")
    print("=" * 60)
    
    try:
        # 初始化演示类
        demo = MemoryDemo()
        
        # 1. 简单内存演示
        demo.simple_memory_demo()
        
        # 2. 缓冲区内存演示
        demo.buffer_memory_demo()
        
        # 3. 摘要内存演示
        demo.summary_memory_demo()
        
        # 4. 自定义内存演示
        demo.custom_memory_demo()
        
        print("\n" + "=" * 60)
        print("🎉 Demo 4 演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保 Ollama 服务正在运行，并且已安装 llama3.2 模型")


if __name__ == "__main__":
    main()
