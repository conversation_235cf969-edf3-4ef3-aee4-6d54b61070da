#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行所有LangChain演示的脚本
用于快速测试所有功能是否正常工作
"""

import subprocess
import sys
import time
import requests


def check_ollama_service():
    """检查Ollama服务是否运行"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False


def check_llama_model():
    """检查llama3.2模型是否可用"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            return any("llama3.2" in model.get("name", "") for model in models)
    except:
        pass
    return False


def run_demo(demo_file, demo_name):
    """运行单个演示"""
    print(f"\n{'='*60}")
    print(f"🚀 运行 {demo_name}")
    print(f"📁 文件: {demo_file}")
    print(f"{'='*60}")
    
    try:
        # 运行演示
        result = subprocess.run([sys.executable, demo_file], 
                              capture_output=True, 
                              text=True, 
                              timeout=300)  # 5分钟超时
        
        if result.returncode == 0:
            print(f"✅ {demo_name} 运行成功！")
            # 只显示最后几行输出
            output_lines = result.stdout.split('\n')
            if len(output_lines) > 10:
                print("📄 输出摘要（最后10行）:")
                for line in output_lines[-10:]:
                    if line.strip():
                        print(f"  {line}")
            else:
                print("📄 完整输出:")
                print(result.stdout)
        else:
            print(f"❌ {demo_name} 运行失败！")
            print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {demo_name} 运行超时（5分钟）")
        return False
    except Exception as e:
        print(f"❌ {demo_name} 运行异常: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("🎯 LangChain 演示项目 - 批量运行脚本")
    print("=" * 60)
    
    # 1. 检查环境
    print("\n🔍 检查运行环境...")
    
    # 检查Ollama服务
    if not check_ollama_service():
        print("❌ Ollama服务未运行！")
        print("请先启动Ollama服务: ollama serve")
        return
    else:
        print("✅ Ollama服务正常运行")
    
    # 检查模型
    if not check_llama_model():
        print("❌ llama3.2模型未找到！")
        print("请先下载模型: ollama pull llama3.2")
        return
    else:
        print("✅ llama3.2模型可用")
    
    # 2. 定义演示列表
    demos = [
        ("demo1_basic_llm.py", "Demo 1: 基础模型输入输出"),
        ("demo2_knowledge_base.py", "Demo 2: 基础知识库"),
        ("demo3_chain_calls.py", "Demo 3: 链式调用"),
        ("demo4_memory.py", "Demo 4: 内存记忆"),
        ("demo5_simple_agent.py", "Demo 5: 简单智能体"),
        ("demo6_callback_logging.py", "Demo 6: 回调函数记录审计日志")
    ]
    
    # 3. 运行演示
    print(f"\n🚀 开始运行 {len(demos)} 个演示...")
    
    success_count = 0
    failed_demos = []
    
    for demo_file, demo_name in demos:
        try:
            if run_demo(demo_file, demo_name):
                success_count += 1
            else:
                failed_demos.append(demo_name)
            
            # 演示间暂停
            time.sleep(2)
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断运行")
            break
    
    # 4. 总结报告
    print("\n" + "=" * 60)
    print("📊 运行总结报告")
    print("=" * 60)
    print(f"✅ 成功运行: {success_count}/{len(demos)} 个演示")
    
    if failed_demos:
        print(f"❌ 失败演示:")
        for demo in failed_demos:
            print(f"  - {demo}")
    else:
        print("🎉 所有演示都运行成功！")
    
    print("\n📝 注意事项:")
    print("- 如果某个演示失败，可以单独运行该文件进行调试")
    print("- 确保网络连接正常，某些演示可能需要下载依赖")
    print("- 如果内存不足，可以尝试使用更小的模型（如llama3.2:1b）")
    
    print("\n🔧 单独运行演示的命令:")
    for demo_file, demo_name in demos:
        print(f"  python {demo_file}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 脚本运行出错: {e}")
        print("请检查Python环境和依赖安装")
