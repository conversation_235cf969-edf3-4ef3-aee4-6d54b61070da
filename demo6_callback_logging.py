#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo 6: <PERSON><PERSON><PERSON><PERSON> 回调函数记录审计日志演示
演示如何使用回调函数记录和监控LLM调用过程
"""

from langchain_ollama import ChatOllama
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.messages import BaseMessage
from typing import Any, Dict, List, Optional
import datetime
import json
import logging
import time


class AuditCallbackHandler(BaseCallbackHandler):
    """审计回调处理器"""
    
    def __init__(self, log_file: str = "langchain_audit.log"):
        """初始化审计回调处理器"""
        self.log_file = log_file
        self.session_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.call_count = 0
        self.start_times = {}
        
        # 配置日志
        logging.basicConfig(
            filename=log_file,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            filemode='a'
        )
        self.logger = logging.getLogger(__name__)
        
        # 记录会话开始
        self.log_event("SESSION_START", {
            "session_id": self.session_id,
            "timestamp": datetime.datetime.now().isoformat()
        })
    
    def log_event(self, event_type: str, data: Dict[str, Any]):
        """记录事件到日志"""
        log_entry = {
            "session_id": self.session_id,
            "event_type": event_type,
            "timestamp": datetime.datetime.now().isoformat(),
            "data": data
        }
        
        # 写入日志文件
        self.logger.info(json.dumps(log_entry, ensure_ascii=False))
        
        # 同时打印到控制台
        print(f"📝 [LOG] {event_type}: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """LLM开始调用时的回调"""
        self.call_count += 1
        call_id = f"call_{self.call_count}"
        self.start_times[call_id] = time.time()
        
        self.log_event("LLM_START", {
            "call_id": call_id,
            "model_info": serialized,
            "prompt_count": len(prompts),
            "prompts": prompts[:2] if len(prompts) <= 2 else prompts[:2] + ["..."],  # 限制日志大小
            "kwargs": {k: v for k, v in kwargs.items() if k not in ['callbacks']}
        })
    
    def on_llm_end(self, response, **kwargs) -> None:
        """LLM调用结束时的回调"""
        call_id = f"call_{self.call_count}"
        end_time = time.time()
        duration = end_time - self.start_times.get(call_id, end_time)
        
        self.log_event("LLM_END", {
            "call_id": call_id,
            "duration_seconds": round(duration, 3),
            "response_length": len(str(response)),
            "token_usage": getattr(response, 'usage_metadata', None),
            "response_preview": str(response)[:200] + "..." if len(str(response)) > 200 else str(response)
        })
    
    def on_llm_error(self, error: Exception, **kwargs) -> None:
        """LLM调用出错时的回调"""
        call_id = f"call_{self.call_count}"
        
        self.log_event("LLM_ERROR", {
            "call_id": call_id,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "kwargs": {k: v for k, v in kwargs.items() if k not in ['callbacks']}
        })
    
    def on_chain_start(self, serialized: Dict[str, Any], inputs: Dict[str, Any], **kwargs) -> None:
        """链开始执行时的回调"""
        self.log_event("CHAIN_START", {
            "chain_info": serialized,
            "inputs": inputs,
            "run_id": kwargs.get('run_id', 'unknown')
        })
    
    def on_chain_end(self, outputs: Dict[str, Any], **kwargs) -> None:
        """链执行结束时的回调"""
        self.log_event("CHAIN_END", {
            "outputs": outputs,
            "run_id": kwargs.get('run_id', 'unknown')
        })
    
    def on_chain_error(self, error: Exception, **kwargs) -> None:
        """链执行出错时的回调"""
        self.log_event("CHAIN_ERROR", {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "run_id": kwargs.get('run_id', 'unknown')
        })
    
    def on_text(self, text: str, **kwargs) -> None:
        """处理文本时的回调"""
        self.log_event("TEXT_PROCESSING", {
            "text_length": len(text),
            "text_preview": text[:100] + "..." if len(text) > 100 else text
        })


class PerformanceCallbackHandler(BaseCallbackHandler):
    """性能监控回调处理器"""
    
    def __init__(self):
        self.metrics = {
            "total_calls": 0,
            "total_duration": 0,
            "average_duration": 0,
            "max_duration": 0,
            "min_duration": float('inf'),
            "error_count": 0
        }
        self.start_times = {}
        self.call_count = 0
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """记录开始时间"""
        self.call_count += 1
        call_id = f"perf_call_{self.call_count}"
        self.start_times[call_id] = time.time()
        self.metrics["total_calls"] += 1
    
    def on_llm_end(self, response, **kwargs) -> None:
        """计算性能指标"""
        call_id = f"perf_call_{self.call_count}"
        if call_id in self.start_times:
            duration = time.time() - self.start_times[call_id]
            
            self.metrics["total_duration"] += duration
            self.metrics["max_duration"] = max(self.metrics["max_duration"], duration)
            self.metrics["min_duration"] = min(self.metrics["min_duration"], duration)
            self.metrics["average_duration"] = self.metrics["total_duration"] / self.metrics["total_calls"]
            
            del self.start_times[call_id]
    
    def on_llm_error(self, error: Exception, **kwargs) -> None:
        """记录错误"""
        self.metrics["error_count"] += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.metrics.copy()


class CallbackLoggingDemo:
    """回调函数记录演示类"""
    
    def __init__(self):
        """初始化演示系统"""
        print("🚀 初始化回调日志系统...")
        
        # 创建回调处理器
        self.audit_handler = AuditCallbackHandler("demo_audit.log")
        self.performance_handler = PerformanceCallbackHandler()
        
        # 初始化模型（带回调）
        self.llm = ChatOllama(
            model="llama3.2",
            base_url="http://localhost:11434",
            temperature=0.3,
            callbacks=[self.audit_handler, self.performance_handler]
        )
        
        print("✅ 回调日志系统初始化完成！")
    
    def simple_logging_demo(self):
        """简单日志记录演示"""
        print("\n" + "=" * 40)
        print("📝 演示1: 基础日志记录")
        print("=" * 40)
        
        # 创建带回调的链
        prompt = ChatPromptTemplate.from_template("请简要回答：{question}")
        chain = prompt | self.llm | StrOutputParser()
        
        # 测试问题
        questions = [
            "什么是人工智能？",
            "Python有哪些优势？",
            "如何学习编程？"
        ]
        
        for question in questions:
            print(f"\n👤 问题: {question}")
            try:
                response = chain.invoke({"question": question})
                print(f"🤖 回答: {response}")
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    def error_logging_demo(self):
        """错误日志记录演示"""
        print("\n" + "=" * 40)
        print("📝 演示2: 错误日志记录")
        print("=" * 40)
        
        # 故意创建一个会出错的场景
        try:
            # 使用错误的模型名称
            error_llm = ChatOllama(
                model="nonexistent_model",
                base_url="http://localhost:11434",
                callbacks=[self.audit_handler]
            )
            
            response = error_llm.invoke("测试错误处理")
        except Exception as e:
            print(f"✅ 成功捕获并记录错误: {e}")
    
    def performance_monitoring_demo(self):
        """性能监控演示"""
        print("\n" + "=" * 40)
        print("📝 演示3: 性能监控")
        print("=" * 40)
        
        # 执行多个调用来收集性能数据
        prompt = ChatPromptTemplate.from_template("用一句话回答：{question}")
        chain = prompt | self.llm | StrOutputParser()
        
        questions = [
            "什么是机器学习？",
            "什么是深度学习？",
            "什么是神经网络？",
            "什么是自然语言处理？",
            "什么是计算机视觉？"
        ]
        
        print("🔄 执行多个查询以收集性能数据...")
        for i, question in enumerate(questions, 1):
            print(f"  查询 {i}/{len(questions)}: {question}")
            response = chain.invoke({"question": question})
            print(f"  回答: {response[:50]}...")
        
        # 显示性能指标
        metrics = self.performance_handler.get_metrics()
        print(f"\n📊 性能指标:")
        print(f"  总调用次数: {metrics['total_calls']}")
        print(f"  总耗时: {metrics['total_duration']:.3f} 秒")
        print(f"  平均耗时: {metrics['average_duration']:.3f} 秒")
        print(f"  最大耗时: {metrics['max_duration']:.3f} 秒")
        print(f"  最小耗时: {metrics['min_duration']:.3f} 秒")
        print(f"  错误次数: {metrics['error_count']}")
    
    def custom_callback_demo(self):
        """自定义回调演示"""
        print("\n" + "=" * 40)
        print("📝 演示4: 自定义回调功能")
        print("=" * 40)
        
        class CustomCallbackHandler(BaseCallbackHandler):
            """自定义回调处理器"""
            
            def __init__(self):
                self.conversation_log = []
            
            def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
                print("🚀 [自定义] LLM调用开始")
                self.conversation_log.append({
                    "type": "start",
                    "timestamp": datetime.datetime.now().isoformat(),
                    "prompts": prompts
                })
            
            def on_llm_end(self, response, **kwargs) -> None:
                print("✅ [自定义] LLM调用完成")
                self.conversation_log.append({
                    "type": "end",
                    "timestamp": datetime.datetime.now().isoformat(),
                    "response": str(response)
                })
            
            def get_conversation_log(self):
                return self.conversation_log
        
        # 创建自定义回调
        custom_handler = CustomCallbackHandler()
        
        # 创建带自定义回调的LLM
        custom_llm = ChatOllama(
            model="llama3.2",
            base_url="http://localhost:11434",
            temperature=0.3,
            callbacks=[custom_handler]
        )
        
        # 测试对话
        response = custom_llm.invoke("请介绍一下LangChain框架")
        print(f"🤖 回答: {response.content}")
        
        # 显示对话日志
        print(f"\n📋 对话日志:")
        for entry in custom_handler.get_conversation_log():
            print(f"  {entry['type']}: {entry['timestamp']}")


def main():
    """主函数 - 演示回调日志功能"""
    print("=" * 60)
    print("🎯 LangChain Demo 6: 回调函数记录审计日志")
    print("=" * 60)
    
    try:
        # 初始化演示类
        demo = CallbackLoggingDemo()
        
        # 1. 基础日志记录
        demo.simple_logging_demo()
        
        # 2. 错误日志记录
        demo.error_logging_demo()
        
        # 3. 性能监控
        demo.performance_monitoring_demo()
        
        # 4. 自定义回调
        demo.custom_callback_demo()
        
        print("\n" + "=" * 60)
        print("🎉 Demo 6 演示完成！")
        print("📁 审计日志已保存到: demo_audit.log")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保 Ollama 服务正在运行，并且已安装 llama3.2 模型")


if __name__ == "__main__":
    main()
