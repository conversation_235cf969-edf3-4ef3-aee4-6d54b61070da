#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo 2: <PERSON><PERSON><PERSON><PERSON> 基础知识库演示
演示如何创建向量数据库，存储和检索文档
"""

from langchain_ollama import ChatOllama, OllamaEmbeddings
from langchain_chroma import Chroma
from langchain_core.documents import Document
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser
import tempfile
import shutil


class KnowledgeBaseDemo:
    """知识库演示类"""
    
    def __init__(self):
        """初始化模型和向量数据库"""
        print("🚀 初始化知识库系统...")
        
        # 初始化大语言模型
        self.llm = ChatOllama(
            model="llama3.2",
            base_url="http://localhost:11434",
            temperature=0.7
        )
        
        # 初始化嵌入模型
        self.embeddings = OllamaEmbeddings(
            model="llama3.2",
            base_url="http://localhost:11434"
        )
        
        # 创建临时目录存储向量数据库
        self.temp_dir = tempfile.mkdtemp()
        self.vectorstore = None
        
        print("✅ 知识库系统初始化完成！")
    
    def create_knowledge_base(self, documents_data: list):
        """创建知识库"""
        print(f"\n📚 创建知识库，包含 {len(documents_data)} 个文档...")
        
        # 创建文档对象
        documents = []
        for i, text in enumerate(documents_data):
            doc = Document(
                page_content=text,
                metadata={"source": f"document_{i+1}", "doc_id": i+1}
            )
            documents.append(doc)
        
        # 创建向量数据库
        self.vectorstore = Chroma.from_documents(
            documents=documents,
            embedding=self.embeddings,
            persist_directory=self.temp_dir
        )
        
        print("✅ 知识库创建完成！")
        return self.vectorstore
    
    def search_knowledge(self, query: str, k: int = 3):
        """搜索知识库"""
        if not self.vectorstore:
            print("❌ 知识库未初始化")
            return []
        
        print(f"\n🔍 搜索查询: {query}")
        
        # 相似性搜索
        results = self.vectorstore.similarity_search(query, k=k)
        
        print(f"📋 找到 {len(results)} 个相关文档:")
        for i, doc in enumerate(results):
            print(f"\n文档 {i+1} (来源: {doc.metadata.get('source', 'unknown')}):")
            print(f"内容: {doc.page_content[:200]}...")
        
        return results
    
    def rag_query(self, question: str):
        """RAG (检索增强生成) 查询"""
        if not self.vectorstore:
            print("❌ 知识库未初始化")
            return None
        
        print(f"\n🤖 RAG查询: {question}")
        
        # 创建检索器
        retriever = self.vectorstore.as_retriever(search_kwargs={"k": 2})
        
        # 创建提示模板
        template = """基于以下上下文信息回答问题。如果上下文中没有相关信息，请说明无法从提供的信息中找到答案。

                    上下文:
                    {context}
                    
                    问题: {question}
                    
                    回答:
                    """
        
        prompt = ChatPromptTemplate.from_template(template)
        
        # 创建RAG链
        def format_docs(docs):
            return "\n\n".join(doc.page_content for doc in docs)
        
        rag_chain = (
            {"context": retriever | format_docs, "question": RunnablePassthrough()}
            | prompt
            | self.llm
            | StrOutputParser()
        )
        
        # 执行查询
        response = rag_chain.invoke(question)
        print(f"🤖 AI回答: {response}")
        
        return response
    
    def add_document(self, text: str, metadata: dict = None):
        """向知识库添加新文档"""
        if not self.vectorstore:
            print("❌ 知识库未初始化")
            return
        
        print(f"\n➕ 添加新文档...")
        
        # 创建新文档
        if metadata is None:
            metadata = {"source": "new_document"}
        
        doc = Document(page_content=text, metadata=metadata)
        
        # 添加到向量数据库
        self.vectorstore.add_documents([doc])
        
        print("✅ 文档添加成功！")
    
    def cleanup(self):
        """清理临时文件"""
        if self.temp_dir:
            shutil.rmtree(self.temp_dir)
            print("🧹 临时文件清理完成")


def main():
    """主函数 - 演示知识库功能"""
    print("=" * 60)
    print("🎯 LangChain Demo 2: 基础知识库")
    print("=" * 60)
    
    demo = None
    try:
        # 初始化演示类
        demo = KnowledgeBaseDemo()
        
        # 准备示例文档
        sample_documents = [
            "Python是一种高级编程语言，由Guido van Rossum在1989年发明。它以简洁易读的语法著称，广泛应用于Web开发、数据科学、人工智能等领域。",
            "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习。常见的机器学习算法包括线性回归、决策树、神经网络等。",
            "LangChain是一个用于开发由大语言模型驱动的应用程序的框架。它提供了链式调用、内存管理、代理等功能，简化了LLM应用的开发过程。",
            "向量数据库是专门用于存储和检索高维向量数据的数据库。在AI应用中，它常用于相似性搜索和推荐系统。常见的向量数据库包括Chroma、Pinecone、Weaviate等。"
        ]
        
        # 1. 创建知识库
        print("\n" + "=" * 40)
        print("📝 演示1: 创建知识库")
        print("=" * 40)
        demo.create_knowledge_base(sample_documents)
        
        # 2. 搜索知识库
        print("\n" + "=" * 40)
        print("📝 演示2: 搜索知识库")
        print("=" * 40)
        demo.search_knowledge("什么是Python？")
        demo.search_knowledge("机器学习算法")
        
        # 3. RAG查询
        print("\n" + "=" * 40)
        print("📝 演示3: RAG查询")
        print("=" * 40)
        demo.rag_query("Python有什么特点？")
        demo.rag_query("LangChain提供了哪些功能？")
        
        # 4. 添加新文档
        print("\n" + "=" * 40)
        print("📝 演示4: 添加新文档")
        print("=" * 40)
        new_doc = "深度学习是机器学习的一个子集，使用多层神经网络来学习数据的复杂模式。它在图像识别、自然语言处理等领域取得了突破性进展。"
        demo.add_document(new_doc, {"source": "deep_learning_doc"})
        
        # 测试新添加的文档
        demo.rag_query("什么是深度学习？")
        
        print("\n" + "=" * 60)
        print("🎉 Demo 2 演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请确保 Ollama 服务正在运行，并且已安装 llama3.2 模型")
    
    finally:
        # 清理资源
        if demo:
            demo.cleanup()


if __name__ == "__main__":
    main()
